import dotenv from 'dotenv';
import { outboundDB } from './src/config/database';
import { Clinic } from './src/models/ClientConfig';

dotenv.config();

async function testDatabaseConnection() {
  try {
    console.log('🔄 Testing outbound database connection...');
    console.log('Connection string:', process.env.MONGODB_URI);
    
    // Wait for connection
    await new Promise((resolve, reject) => {
      outboundDB.once('open', () => {
        console.log('✅ Outbound database connected successfully!');
        resolve(true);
      });
      
      outboundDB.once('error', (error) => {
        console.error('❌ Connection failed:', error.message);
        reject(error);
      });
      
      // Timeout after 10 seconds
      setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);
    });
    
    // Test reading data
    console.log('\n📊 Testing data access:');
    const clinics = await Clinic.find({});
    console.log(`Found ${clinics.length} clinics in database`);
    
    if (clinics.length > 0) {
      console.log('Sample clinic:', {
        name: clinics[0].clinic_name || clinics[0].name,
        active: clinics[0].is_active || clinics[0].enabled
      });
    }
    
    console.log('\n🎉 Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await outboundDB.close();
    process.exit(0);
  }
}

testDatabaseConnection();